{"name": "chat-context-manager", "displayName": "Chat Context Manager", "description": "Save and restore AI chat conversations in VSCode", "version": "0.1.3", "publisher": "chat-context-manager", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mohama<PERSON><PERSON><PERSON><PERSON>/chat-context-manager"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:chatContextManager.saveSession", "onCommand:chatContextManager.showSessions", "onCommand:chatContextManager.generateMegaPrompt"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "chatContextManager.saveSession", "title": "Save Chat Session", "category": "Chat Context"}, {"command": "chatContextManager.showSessions", "title": "Show Saved Sessions", "category": "Chat Context"}, {"command": "chatContextManager.generateMegaPrompt", "title": "Generate Mega Prompt", "category": "Chat Context"}], "menus": {"commandPalette": [{"command": "chatContextManager.saveSession"}, {"command": "chatContextManager.showSessions"}, {"command": "chatContextManager.generateMegaPrompt"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}