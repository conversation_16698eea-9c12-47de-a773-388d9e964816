# Chat Context Manager

A VSCode extension that helps you save and restore AI chat conversations, making it easy to maintain context across different AI tools and sessions.

## Features

- 💾 **Save Chat Sessions**: Capture and save your AI conversations with timestamps
- 📋 **Generate Mega Prompts**: Convert saved conversations into reusable prompts
- 🔍 **Browse Sessions**: View and manage all your saved chat sessions
- 🗑️ **Session Management**: Delete unwanted sessions
- 🔒 **Local Storage**: All data is stored locally for privacy

## How to Use

### 1. Save a Chat Session
1. Copy your chat conversation to clipboard OR select the text in VSCode
2. Open Command Palette (`Cmd+Shift+P` / `Ctrl+Shift+P`)
3. Run command: `Chat Context: Save Chat Session`
4. Enter a name for your session
5. Session is saved locally!

### 2. View Saved Sessions
1. Open Command Palette
2. Run command: `Chat Context: Show Saved Sessions`
3. Browse your saved conversations in the webview panel

### 3. Generate Mega Prompt
1. From the sessions panel, click "Copy Mega Prompt" on any session
2. OR use Command Palette: `Chat Context: Generate Mega Prompt`
3. Select a session from the list
4. The mega prompt is copied to your clipboard - ready to paste into any AI tool!

## Supported AI Tools

This extension works with any AI chat tool that uses text-based conversations:
- Cursor Chat
- Augment AI
- WindSurf
- ChatGPT
- Claude
- And any other text-based AI conversation

## Installation

### From Source
1. Clone this repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VSCode window with the extension loaded

### From VSIX (Coming Soon)
The extension will be available on the VSCode Marketplace soon.

## Privacy

- All chat data is stored locally on your machine
- No data is sent to external servers
- Sessions are stored in your VSCode global storage directory

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

MIT License - see LICENSE file for details.
