# Chat Context Manager

A powerful VSCode extension that helps you save, organize, and restore AI chat conversations with advanced features for better context management across different AI tools and sessions.

## ✨ Features

### Core Features
- 💾 **Smart Chat Session Saving**: Automatically detect and save conversations from multiple AI tools
- 📋 **Mega Prompt Generation**: Convert saved conversations into reusable context prompts
- 🔍 **Advanced Search & Filtering**: Find sessions by content, tags, categories, dates, and formats
- 🏷️ **Tags & Categories**: Organize sessions with custom tags and categories
- 📊 **Statistics & Analytics**: Track your chat usage with detailed statistics
- 🔒 **Local Privacy**: All data stored locally on your machine

### Enhanced Detection
- 🤖 **Multi-Format Support**: Supports Augment AI, Cursor, Claude, ChatGPT, and generic formats
- 🎯 **Smart Format Detection**: Automatically identifies chat format with confidence scoring
- 📈 **Real-time Monitoring**: Optional background monitoring for new chat content
- 🔧 **Flexible Input Methods**: Clipboard, text selection, file detection, and manual input

## 🚀 How to Use

### 1. Save a Chat Session
1. **Automatic Detection**: Copy your chat conversation to clipboard OR select text in VSCode
2. **Open Command Palette**: `Cmd+Shift+P` / `Ctrl+Shift+P`
3. **Run Command**: `Chat Context: Save Chat Session`
4. **Add Details**:
   - Enter a descriptive name
   - Add tags (comma-separated, optional)
   - Choose or create a category
5. **Done!** Session saved with format detection and metadata

### 2. Browse & Search Sessions
1. **View All**: `Chat Context: Show Saved Sessions`
2. **Search**: `Chat Context: Search Sessions` - search by content, name, or tags
3. **Filter**: Browse by tags, categories, or date ranges in the webview
4. **Organize**: Add/remove tags and update categories directly

### 3. Generate Mega Prompts
1. **From Sessions Panel**: Click "Copy Mega Prompt" on any session
2. **From Command Palette**: `Chat Context: Generate Mega Prompt`
3. **Select Session**: Choose from your saved conversations
4. **Ready to Use**: Mega prompt copied to clipboard with full context

### 4. Monitor Chat Activity
1. **Start Monitoring**: `Chat Context: Start Chat Monitoring`
2. **Background Detection**: Automatically detects new chat content
3. **Stop When Done**: `Chat Context: Stop Chat Monitoring`

### 5. View Statistics
- **Usage Stats**: `Chat Context: Show Statistics`
- **Track Progress**: See total sessions, messages, words, and format distribution

## Supported AI Tools

This extension works with any AI chat tool that uses text-based conversations:
- Cursor Chat
- Augment AI
- WindSurf
- ChatGPT
- Claude
- And any other text-based AI conversation

## Installation

### From Source
1. Clone this repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VSCode window with the extension loaded

### From VSIX (Coming Soon)
The extension will be available on the VSCode Marketplace soon.

## Privacy

- All chat data is stored locally on your machine
- No data is sent to external servers
- Sessions are stored in your VSCode global storage directory

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

MIT License - see LICENSE file for details.
