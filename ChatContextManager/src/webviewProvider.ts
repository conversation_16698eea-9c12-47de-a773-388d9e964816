import * as vscode from 'vscode';
import * as path from 'path';
import { SessionManager, ChatSession } from './sessionManager';

export class WebviewProvider {
    private panel: vscode.WebviewPanel | undefined;

    constructor(
        private context: vscode.ExtensionContext,
        private sessionManager: SessionManager
    ) {}

    public showSessions() {
        if (this.panel) {
            this.panel.reveal();
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'chatSessions',
            'Chat Sessions',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.panel.webview.html = this.getWebviewContent();
        
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'getSessions':
                        const allSessions = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: allSessions
                        });
                        break;

                    case 'deleteSession':
                        await this.sessionManager.deleteSession(message.sessionId);
                        vscode.window.showInformationMessage('Session deleted successfully!');
                        // Refresh the sessions list
                        const updatedSessions = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: updatedSessions
                        });
                        break;

                    case 'generateMegaPrompt':
                        const sessionsForPrompt = await this.sessionManager.getAllSessions();
                        const targetSession = sessionsForPrompt.find(s => s.id === message.sessionId);
                        if (targetSession) {
                            const megaPrompt = this.sessionManager.generateMegaPrompt(targetSession);
                            await vscode.env.clipboard.writeText(megaPrompt);
                            vscode.window.showInformationMessage('Mega prompt copied to clipboard!');
                        }
                        break;

                    case 'viewDetails':
                        const allSessionsForDetails = await this.sessionManager.getAllSessions();
                        const sessionForDetails = allSessionsForDetails.find(s => s.id === message.sessionId);
                        if (sessionForDetails) {
                            this.showSessionDetails(sessionForDetails);
                        }
                        break;

                    case 'addTag':
                        await this.sessionManager.addTagToSession(message.sessionId, message.tag);
                        vscode.window.showInformationMessage(`Tag "${message.tag}" added successfully!`);
                        // Refresh the sessions list
                        const sessionsAfterTag = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: sessionsAfterTag
                        });
                        break;

                    case 'removeTag':
                        await this.sessionManager.removeTagFromSession(message.sessionId, message.tag);
                        vscode.window.showInformationMessage(`Tag "${message.tag}" removed successfully!`);
                        // Refresh the sessions list
                        const sessionsAfterRemove = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: sessionsAfterRemove
                        });
                        break;

                    case 'updateCategory':
                        await this.sessionManager.updateSession(message.sessionId, { category: message.category });
                        vscode.window.showInformationMessage(`Category updated to "${message.category}"!`);
                        // Refresh the sessions list
                        const sessionsAfterCategory = await this.sessionManager.getAllSessions();
                        this.panel?.webview.postMessage({
                            command: 'sessionsData',
                            sessions: sessionsAfterCategory
                        });
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });
    }

    public showSessionDetails(session: ChatSession) {
        let details = `Session: ${session.name}\n`;
        details += `Date: ${new Date(session.timestamp).toLocaleString()}\n`;
        details += `Messages: ${session.messages.length}\n`;

        if ((session as any).requestIds && (session as any).requestIds.length > 0) {
            details += `Request IDs: ${(session as any).requestIds.join(', ')}\n`;
        }

        if ((session as any).txtFilePath) {
            details += `TXT File: ${(session as any).txtFilePath}\n`;
        }

        details += '\n--- MESSAGES ---\n\n';

        session.messages.forEach((msg, index) => {
            details += `${index + 1}. ${msg.role.toUpperCase()}:\n`;
            details += `${msg.content}\n\n`;
            details += '---\n\n';
        });

        vscode.window.showInformationMessage(
            `Session Details:\n${details.substring(0, 500)}${details.length > 500 ? '...\n\n[Full details saved in TXT file]' : ''}`,
            { modal: true },
            'OK'
        );
    }

    private getWebviewContent(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Sessions</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }

        .header {
            margin-bottom: 20px;
        }

        h1 {
            color: var(--vscode-titleBar-activeForeground);
            margin-bottom: 15px;
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-container {
            display: flex;
            align-items: center;
            position: relative;
        }

        #search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 14px;
        }

        #search-input:focus {
            outline: 1px solid var(--vscode-focusBorder);
            border-color: var(--vscode-focusBorder);
        }

        #clear-search {
            position: absolute;
            right: 8px;
            background: none;
            border: none;
            color: var(--vscode-descriptionForeground);
            cursor: pointer;
            padding: 4px;
            border-radius: 2px;
        }

        #clear-search:hover {
            background: var(--vscode-toolbar-hoverBackground);
        }

        .filters {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filters select {
            padding: 6px 10px;
            border: 1px solid var(--vscode-input-border);
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 13px;
            min-width: 120px;
        }

        .stats-bar {
            display: flex;
            gap: 20px;
            padding: 10px 15px;
            background: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 13px;
            font-weight: 500;
        }
        
        .session-item {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: var(--vscode-editor-background);
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .session-name {
            font-size: 16px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .session-date {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .session-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .delete-btn {
            background-color: var(--vscode-errorForeground);
        }
        
        .delete-btn:hover {
            background-color: var(--vscode-errorForeground);
            opacity: 0.8;
        }
        
        .session-details {
            margin-bottom: 10px;
        }

        .message-count, .request-ids, .txt-file, .word-count, .format, .category {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 5px;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
            margin-bottom: 5px;
        }

        .tag {
            background: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .category {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
        }

        .format {
            background: var(--vscode-inputValidation-infoBackground);
            color: var(--vscode-inputValidation-infoForeground);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
        }

        .messages-preview {
            background-color: var(--vscode-textBlockQuote-background);
            border-left: 3px solid var(--vscode-textBlockQuote-border);
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .message-preview {
            font-size: 12px;
            margin-bottom: 5px;
            color: var(--vscode-editor-foreground);
        }

        .message-preview strong {
            color: var(--vscode-textLink-foreground);
        }

        .more-messages {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
        
        .no-sessions {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            margin-top: 50px;
        }
        
        .loading {
            text-align: center;
            color: var(--vscode-descriptionForeground);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💬 Saved Chat Sessions</h1>
        <div class="controls">
            <div class="search-container">
                <input type="text" id="search-input" placeholder="🔍 Search sessions..." />
                <button id="clear-search" title="Clear search">✕</button>
            </div>
            <div class="filters">
                <select id="category-filter">
                    <option value="">All Categories</option>
                </select>
                <select id="format-filter">
                    <option value="">All Formats</option>
                </select>
                <select id="sort-filter">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="name">Name A-Z</option>
                    <option value="messages">Most Messages</option>
                </select>
            </div>
        </div>
    </div>

    <div class="stats-bar" id="stats-bar">
        <span id="session-count">0 sessions</span>
        <span id="total-messages">0 messages</span>
        <span id="total-words">0 words</span>
    </div>

    <div id="sessions-container">
        <div class="loading">Loading sessions...</div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let allSessions = [];
        let filteredSessions = [];

        // Request sessions data when page loads
        vscode.postMessage({ command: 'getSessions' });

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            if (message.command === 'sessionsData') {
                allSessions = message.sessions;
                filteredSessions = [...allSessions];
                populateFilters();
                updateStats();
                displaySessions(filteredSessions);
            }
        });

        // Search and filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const clearSearch = document.getElementById('clear-search');
            const categoryFilter = document.getElementById('category-filter');
            const formatFilter = document.getElementById('format-filter');
            const sortFilter = document.getElementById('sort-filter');

            searchInput.addEventListener('input', filterSessions);
            clearSearch.addEventListener('click', clearSearch);
            categoryFilter.addEventListener('change', filterSessions);
            formatFilter.addEventListener('change', filterSessions);
            sortFilter.addEventListener('change', filterSessions);
        });

        function populateFilters() {
            const categoryFilter = document.getElementById('category-filter');
            const formatFilter = document.getElementById('format-filter');

            // Populate categories
            const categories = [...new Set(allSessions.map(s => s.category).filter(Boolean))];
            categoryFilter.innerHTML = '<option value="">All Categories</option>' +
                categories.map(cat => \`<option value="\${cat}">\${cat}</option>\`).join('');

            // Populate formats
            const formats = [...new Set(allSessions.map(s => s.format).filter(Boolean))];
            formatFilter.innerHTML = '<option value="">All Formats</option>' +
                formats.map(fmt => \`<option value="\${fmt}">\${fmt}</option>\`).join('');
        }

        function filterSessions() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;
            const formatFilter = document.getElementById('format-filter').value;
            const sortFilter = document.getElementById('sort-filter').value;

            filteredSessions = allSessions.filter(session => {
                // Text search
                const matchesSearch = !searchTerm ||
                    session.name.toLowerCase().includes(searchTerm) ||
                    session.messages.some(msg => msg.content.toLowerCase().includes(searchTerm)) ||
                    (session.tags && session.tags.some(tag => tag.toLowerCase().includes(searchTerm)));

                // Category filter
                const matchesCategory = !categoryFilter || session.category === categoryFilter;

                // Format filter
                const matchesFormat = !formatFilter || session.format === formatFilter;

                return matchesSearch && matchesCategory && matchesFormat;
            });

            // Sort sessions
            filteredSessions.sort((a, b) => {
                switch (sortFilter) {
                    case 'oldest':
                        return a.timestamp - b.timestamp;
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'messages':
                        return b.messages.length - a.messages.length;
                    default: // newest
                        return (b.lastModified || b.timestamp) - (a.lastModified || a.timestamp);
                }
            });

            updateStats();
            displaySessions(filteredSessions);
        }

        function clearSearchInput() {
            document.getElementById('search-input').value = '';
            filterSessions();
        }

        function updateStats() {
            const sessionCount = filteredSessions.length;
            const totalMessages = filteredSessions.reduce((sum, s) => sum + s.messages.length, 0);
            const totalWords = filteredSessions.reduce((sum, s) => sum + (s.wordCount || 0), 0);

            document.getElementById('session-count').textContent = \`\${sessionCount} session\${sessionCount !== 1 ? 's' : ''}\`;
            document.getElementById('total-messages').textContent = \`\${totalMessages.toLocaleString()} message\${totalMessages !== 1 ? 's' : ''}\`;
            document.getElementById('total-words').textContent = \`\${totalWords.toLocaleString()} word\${totalWords !== 1 ? 's' : ''}\`;
        }
        
        function displaySessions(sessions) {
            const container = document.getElementById('sessions-container');
            
            if (sessions.length === 0) {
                container.innerHTML = '<div class="no-sessions">No saved sessions found. Save a chat session to get started!</div>';
                return;
            }
            
            container.innerHTML = sessions.map(session =>
                \`<div class="session-item">
                    <div class="session-header">
                        <div class="session-name">\${session.name}</div>
                        <div class="session-date">\${new Date(session.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="session-details">
                        <div class="message-count">📝 \${session.messages.length} messages</div>
                        \${session.wordCount ? \`<div class="word-count">📊 \${session.wordCount.toLocaleString()} words</div>\` : ''}
                        \${session.format ? \`<div class="format">🔧 \${session.format} format</div>\` : ''}
                        \${session.category ? \`<div class="category">📁 \${session.category}</div>\` : ''}
                        \${session.tags && session.tags.length > 0 ?
                            \`<div class="tags">🏷️ \${session.tags.map(tag => \`<span class="tag">\${tag}</span>\`).join(' ')}</div>\` : ''
                        }
                        \${session.requestIds && session.requestIds.length > 0 ?
                            \`<div class="request-ids">🔗 \${session.requestIds.length} request IDs</div>\` : ''
                        }
                        \${session.txtFilePath ?
                            \`<div class="txt-file">📄 TXT file saved</div>\` : ''
                        }
                    </div>
                    <div class="messages-preview">
                        \${session.messages.slice(0, 2).map((msg, idx) =>
                            \`<div class="message-preview">
                                <strong>\${msg.role.toUpperCase()}:</strong>
                                \${msg.content.substring(0, 80)}\${msg.content.length > 80 ? '...' : ''}
                            </div>\`
                        ).join('')}
                        \${session.messages.length > 2 ? \`<div class="more-messages">... and \${session.messages.length - 2} more messages</div>\` : ''}
                    </div>
                    <div class="session-actions">
                        <button onclick="generateMegaPrompt('\${session.id}')">📋 Copy Mega Prompt</button>
                        <button onclick="viewDetails('\${session.id}')">👁️ View Details</button>
                        <button class="delete-btn" onclick="deleteSession('\${session.id}')">🗑️ Delete</button>
                    </div>
                </div>\`
            ).join('');
        }
        
        function generateMegaPrompt(sessionId) {
            vscode.postMessage({ 
                command: 'generateMegaPrompt', 
                sessionId: sessionId 
            });
        }
        
        function deleteSession(sessionId) {
            if (confirm('Are you sure you want to delete this session?')) {
                vscode.postMessage({
                    command: 'deleteSession',
                    sessionId: sessionId
                });
            }
        }

        function viewDetails(sessionId) {
            vscode.postMessage({
                command: 'viewDetails',
                sessionId: sessionId
            });
        }
    </script>
</body>
</html>`;
    }
}
