{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,qDAAkD;AAClD,+CAA4C;AAC5C,uDAAoD;AAEpD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IACtC,MAAM,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAErE,oBAAoB;IACpB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QACpG,IAAI;YACA,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAC9D,IAAI,WAAW,EAAE;gBACb,+BAA+B;gBAC/B,MAAM,QAAQ,GAAG,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBAC7D,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBAEhE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uEAAuE,CAAC,CAAC;oBAC1G,OAAO;iBACV;gBAED,uBAAuB;gBACvB,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBACjD,MAAM,EAAE,uCAAuC,QAAQ,CAAC,MAAM,cAAc,UAAU,CAAC,MAAM,mBAAmB;oBAChH,WAAW,EAAE,iBAAiB;iBACjC,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW;oBAAE,OAAO;gBAEzB,0BAA0B;gBAC1B,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC/C,MAAM,EAAE,yDAAyD;oBACjE,WAAW,EAAE,+BAA+B;iBAC/C,CAAC,CAAC;gBAEH,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAExG,8BAA8B;gBAC9B,MAAM,kBAAkB,GAAG,MAAM,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBACnE,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,kBAAkB,CAAC,CAAC;gBACtH,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;gBAEvD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAC9C,CAAC,GAAG,gBAAgB,EAAE,4BAA4B,CAAC,EACnD;oBACI,WAAW,EAAE,+CAA+C;oBAC5D,cAAc,EAAE,IAAI;iBACvB,CACJ,CAAC;gBAEF,IAAI,aAAa,GAAG,SAAS,CAAC;gBAC9B,IAAI,QAAQ,IAAI,QAAQ,KAAK,4BAA4B,EAAE;oBACvD,aAAa,GAAG,QAAQ,CAAC;iBAC5B;qBAAM,IAAI,QAAQ,KAAK,4BAA4B,EAAE;oBAClD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;wBACjD,MAAM,EAAE,yBAAyB;wBACjC,WAAW,EAAE,aAAa;qBAC7B,CAAC,CAAC;oBACH,IAAI,WAAW,EAAE;wBACb,aAAa,GAAG,WAAW,CAAC;qBAC/B;iBACJ;gBAED,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;gBACrG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,cAAc,WAAW,0BAA0B,QAAQ,CAAC,MAAM,cAAc,UAAU,CAAC,MAAM,YAAY,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAC9J,CAAC;aACL;iBAAM;gBACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,wGAAwG,EACxG,mBAAmB,EACnB,WAAW,CACd,CAAC;gBAEF,IAAI,MAAM,KAAK,mBAAmB,EAAE;oBAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mIAAmI,CACtI,CAAC;iBACL;qBAAM,IAAI,MAAM,KAAK,WAAW,EAAE;oBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;iBACpE;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAChG,eAAe,CAAC,YAAY,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QAClH,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;YACjE,OAAO;SACV;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,IAAI;YACnB,WAAW,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE;YACzD,OAAO,EAAE,OAAO;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE;YACjE,WAAW,EAAE,0CAA0C;SAC1D,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YACd,MAAM,UAAU,GAAG,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;SAC5E;IACL,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QAC1G,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,gEAAgE;YACxE,WAAW,EAAE,sBAAsB;SACtC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACP,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,KAAK,GAAG,CAAC,CAAC;gBACzE,OAAO;aACV;YAED,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACzC,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,WAAW,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,eAAe,OAAO,CAAC,QAAQ,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,EAAE;gBAC9H,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;gBAC7C,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC,CAAC;YAEJ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE;gBACjE,WAAW,EAAE,SAAS,OAAO,CAAC,MAAM,kBAAkB,KAAK,GAAG;aACjE,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE;gBACd,eAAe,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aAC5D;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QAChG,MAAM,KAAK,GAAG,MAAM,cAAc,CAAC,eAAe,EAAE,CAAC;QACrD,MAAM,OAAO,GAAG;;kBAEN,KAAK,CAAC,aAAa;kBACnB,KAAK,CAAC,aAAa;eACtB,KAAK,CAAC,UAAU,CAAC,cAAc,EAAE;gCAChB,KAAK,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;;;EAGxE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGrG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAExG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,GAAG,EAAE;QACtG,WAAW,CAAC,eAAe,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACpG,WAAW,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,mBAAmB,EACnB,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,sBAAsB,EACtB,qBAAqB,CACxB,CAAC;AACN,CAAC;AAxLD,4BAwLC;AAED,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}