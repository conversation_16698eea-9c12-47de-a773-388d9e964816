{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,qDAAkD;AAClD,+CAA4C;AAC5C,uDAAoD;AAEpD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IACtC,MAAM,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAErE,oBAAoB;IACpB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QACpG,IAAI;YACA,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAC9D,IAAI,WAAW,EAAE;gBACb,+BAA+B;gBAC/B,MAAM,QAAQ,GAAG,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBAE7D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uEAAuE,CAAC,CAAC;oBAC1G,OAAO;iBACV;gBAED,gCAAgC;gBAChC,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBACjD,MAAM,EAAE,uCAAuC,QAAQ,CAAC,MAAM,kBAAkB;oBAChF,WAAW,EAAE,iBAAiB;iBACjC,CAAC,CAAC;gBAEH,IAAI,WAAW,EAAE;oBACb,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;oBAChF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,cAAc,WAAW,0BAA0B,QAAQ,CAAC,MAAM,8BAA8B,CACnG,CAAC;iBACL;aACJ;iBAAM;gBACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,wGAAwG,EACxG,mBAAmB,EACnB,WAAW,CACd,CAAC;gBAEF,IAAI,MAAM,KAAK,mBAAmB,EAAE;oBAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mIAAmI,CACtI,CAAC;iBACL;qBAAM,IAAI,MAAM,KAAK,WAAW,EAAE;oBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;iBACpE;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACtE;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAChG,eAAe,CAAC,YAAY,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QAClH,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;YACjE,OAAO;SACV;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,IAAI;YACnB,WAAW,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE;YACzD,OAAO,EAAE,OAAO;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE;YACjE,WAAW,EAAE,0CAA0C;SAC1D,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YACd,MAAM,UAAU,GAAG,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;SAC5E;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,mBAAmB,EACnB,yBAAyB,CAC5B,CAAC;AACN,CAAC;AArFD,4BAqFC;AAED,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}