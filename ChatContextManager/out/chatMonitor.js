"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMonitor = void 0;
const vscode = require("vscode");
class ChatMonitor {
    constructor() { }
    async getCurrentChatContent() {
        try {
            // Method 1: Try to get content from active text editor (if user has selected chat content)
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && activeEditor.selection && !activeEditor.selection.isEmpty) {
                const selectedText = activeEditor.document.getText(activeEditor.selection);
                if (this.looksLikeChatContent(selectedText)) {
                    return selectedText;
                }
            }
            // Method 2: Try to get content from clipboard (user can copy chat content first)
            const clipboardContent = await vscode.env.clipboard.readText();
            if (clipboardContent && this.looksLikeChatContent(clipboardContent)) {
                const shouldUseClipboard = await vscode.window.showQuickPick(['Yes', 'No'], {
                    placeHolder: 'Use clipboard content as chat session?',
                    ignoreFocusOut: true
                });
                if (shouldUseClipboard === 'Yes') {
                    return clipboardContent;
                }
            }
            // Method 3: Ask user to manually input or paste content
            const manualInput = await vscode.window.showInputBox({
                prompt: 'Paste your chat conversation here (format: User: message, Assistant: response)',
                placeHolder: 'User: Hello\nAssistant: Hi there! How can I help you?',
                ignoreFocusOut: true
            });
            if (manualInput && manualInput.trim().length > 0) {
                return manualInput;
            }
            return null;
        }
        catch (error) {
            console.error('Error getting chat content:', error);
            return null;
        }
    }
    looksLikeChatContent(content) {
        if (!content || content.length < 10) {
            return false;
        }
        // Check for common chat patterns including Augment formats
        const chatPatterns = [
            /user\s*:/i,
            /assistant\s*:/i,
            /👤/,
            /🤖/,
            /human\s*:/i,
            /ai\s*:/i,
            /you\s*:/i,
            /me\s*:/i,
            /\*\*user\*\*/i,
            /\*\*assistant\*\*/i,
            /\*\*human\*\*/i,
            /\*\*ai\*\*/i,
            /augment\s*:/i,
            /claude\s*:/i,
            /gpt\s*:/i,
            />\s*user/i,
            />\s*assistant/i,
            />\s*human/i,
            />\s*ai/i
        ];
        // Count how many patterns match
        const matchCount = chatPatterns.filter(pattern => pattern.test(content)).length;
        // Check for UUID patterns (request IDs)
        const hasUUIDs = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i.test(content);
        // If we have chat patterns, UUIDs, or content is long enough, consider it chat content
        return matchCount >= 1 || hasUUIDs || content.length > 50;
    }
    // Future enhancement: Monitor specific VSCode panels/webviews
    // This would require more complex implementation to hook into
    // specific AI extension panels like Cursor, Augment, etc.
    async monitorAIExtensionPanels() {
        // This is a placeholder for future implementation
        // Would need to:
        // 1. Detect active AI extension panels
        // 2. Extract content from their webviews
        // 3. Parse the conversation structure
        // For now, we rely on user interaction (selection/clipboard)
        return null;
    }
}
exports.ChatMonitor = ChatMonitor;
//# sourceMappingURL=chatMonitor.js.map