"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMonitor = void 0;
const vscode = require("vscode");
class ChatMonitor {
    constructor() {
        this.supportedFormats = [];
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.initializeSupportedFormats();
    }
    initializeSupportedFormats() {
        this.supportedFormats = [
            // Augment AI format
            {
                name: 'Augment AI',
                patterns: [
                    /Human:\s*/i,
                    /Assistant:\s*/i,
                    /\[Request ID:\s*[a-f0-9-]+\]/i
                ],
                parser: (content) => this.parseAugmentFormat(content),
                confidence: 0.9
            },
            // Cursor format
            {
                name: 'Cursor',
                patterns: [
                    /\*\*User\*\*/i,
                    /\*\*Cursor\*\*/i,
                    /\*\*Assistant\*\*/i
                ],
                parser: (content) => this.parseCursorFormat(content),
                confidence: 0.9
            },
            // Claude format
            {
                name: '<PERSON>',
                patterns: [
                    /Human:\s*/i,
                    /Claude:\s*/i,
                    /Assistant:\s*/i
                ],
                parser: (content) => this.parseClaudeFormat(content),
                confidence: 0.8
            },
            // ChatGPT format
            {
                name: 'ChatGPT',
                patterns: [
                    /You:\s*/i,
                    /ChatGPT:\s*/i,
                    /User:\s*/i,
                    /Assistant:\s*/i
                ],
                parser: (content) => this.parseChatGPTFormat(content),
                confidence: 0.8
            },
            // Generic format
            {
                name: 'Generic',
                patterns: [
                    /user\s*:/i,
                    /assistant\s*:/i,
                    /ai\s*:/i,
                    /human\s*:/i
                ],
                parser: (content) => this.parseGenericFormat(content),
                confidence: 0.6
            }
        ];
    }
    async getCurrentChatContent() {
        try {
            // Method 1: Try to get content from active text editor (if user has selected chat content)
            const selectedContent = await this.getSelectedContent();
            if (selectedContent) {
                return selectedContent;
            }
            // Method 2: Try to get content from clipboard with smart detection
            const clipboardContent = await this.getClipboardContent();
            if (clipboardContent) {
                return clipboardContent;
            }
            // Method 3: Try to detect from open tabs/files
            const detectedContent = await this.detectFromOpenFiles();
            if (detectedContent) {
                return detectedContent;
            }
            // Method 4: Ask user to manually input or paste content
            const manualContent = await this.getManualInput();
            if (manualContent) {
                return manualContent;
            }
            return null;
        }
        catch (error) {
            console.error('Error getting chat content:', error);
            vscode.window.showErrorMessage(`Error getting chat content: ${error}`);
            return null;
        }
    }
    async getSelectedContent() {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && activeEditor.selection && !activeEditor.selection.isEmpty) {
            const selectedText = activeEditor.document.getText(activeEditor.selection);
            if (this.looksLikeChatContent(selectedText)) {
                return selectedText;
            }
        }
        return null;
    }
    async getClipboardContent() {
        const clipboardContent = await vscode.env.clipboard.readText();
        if (clipboardContent && this.looksLikeChatContent(clipboardContent)) {
            const format = this.detectChatFormat(clipboardContent);
            const shouldUseClipboard = await vscode.window.showQuickPick(['Yes', 'No'], {
                placeHolder: `Use clipboard content as chat session? (Detected: ${format.name})`,
                ignoreFocusOut: true
            });
            if (shouldUseClipboard === 'Yes') {
                return clipboardContent;
            }
        }
        return null;
    }
    async detectFromOpenFiles() {
        // Check all open text documents for chat content
        const openDocuments = vscode.workspace.textDocuments;
        for (const document of openDocuments) {
            if (document.languageId === 'plaintext' || document.languageId === 'markdown') {
                const content = document.getText();
                if (this.looksLikeChatContent(content)) {
                    const shouldUse = await vscode.window.showQuickPick(['Yes', 'No'], {
                        placeHolder: `Found chat content in "${document.fileName}". Use this?`,
                        ignoreFocusOut: true
                    });
                    if (shouldUse === 'Yes') {
                        return content;
                    }
                }
            }
        }
        return null;
    }
    async getManualInput() {
        const manualInput = await vscode.window.showInputBox({
            prompt: 'Paste your chat conversation here (supports multiple formats)',
            placeHolder: 'Human: Hello\nAssistant: Hi there! How can I help you?',
            ignoreFocusOut: true
        });
        if (manualInput && manualInput.trim().length > 0) {
            return manualInput;
        }
        return null;
    }
    looksLikeChatContent(content) {
        if (!content || content.length < 10) {
            return false;
        }
        const format = this.detectChatFormat(content);
        return format.confidence > 0.5;
    }
    detectChatFormat(content) {
        let bestMatch = {
            name: 'Unknown',
            patterns: [],
            parser: () => false,
            confidence: 0
        };
        for (const format of this.supportedFormats) {
            const matchCount = format.patterns.filter(pattern => pattern.test(content)).length;
            const confidence = (matchCount / format.patterns.length) * format.confidence;
            if (confidence > bestMatch.confidence) {
                bestMatch = { ...format, confidence };
            }
        }
        // Additional checks for better detection
        const hasUUIDs = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i.test(content);
        const hasTimestamps = /\d{4}-\d{2}-\d{2}|\d{1,2}:\d{2}/.test(content);
        const hasConversationFlow = /\n\s*\n/.test(content); // Multiple paragraphs
        if (hasUUIDs)
            bestMatch.confidence += 0.2;
        if (hasTimestamps)
            bestMatch.confidence += 0.1;
        if (hasConversationFlow)
            bestMatch.confidence += 0.1;
        return bestMatch;
    }
    // Format-specific parsers
    parseAugmentFormat(content) {
        // Augment typically uses "Human:" and "Assistant:" with Request IDs
        const hasHumanAssistant = /Human:\s*/.test(content) && /Assistant:\s*/.test(content);
        const hasRequestId = /\[Request ID:\s*[a-f0-9-]+\]/i.test(content);
        return hasHumanAssistant || hasRequestId;
    }
    parseCursorFormat(content) {
        // Cursor uses **User** and **Cursor** or **Assistant**
        const hasCursorMarkers = /\*\*User\*\*/i.test(content) &&
            (/\*\*Cursor\*\*/i.test(content) || /\*\*Assistant\*\*/i.test(content));
        return hasCursorMarkers;
    }
    parseClaudeFormat(content) {
        // Claude uses "Human:" and "Claude:" or "Assistant:"
        const hasClaudeMarkers = /Human:\s*/i.test(content) &&
            (/Claude:\s*/i.test(content) || /Assistant:\s*/i.test(content));
        return hasClaudeMarkers;
    }
    parseChatGPTFormat(content) {
        // ChatGPT uses "You:" and "ChatGPT:" or similar
        const hasChatGPTMarkers = (/You:\s*/i.test(content) || /User:\s*/i.test(content)) &&
            (/ChatGPT:\s*/i.test(content) || /Assistant:\s*/i.test(content));
        return hasChatGPTMarkers;
    }
    parseGenericFormat(content) {
        // Generic format with user/assistant patterns
        const hasGenericMarkers = /user\s*:/i.test(content) && /assistant\s*:/i.test(content);
        const hasRoleIndicators = /👤|🤖/.test(content);
        return hasGenericMarkers || hasRoleIndicators;
    }
    // Enhanced monitoring capabilities
    startMonitoring(intervalMs = 5000) {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(async () => {
            try {
                await this.checkForNewChatContent();
            }
            catch (error) {
                console.error('Error during monitoring:', error);
            }
        }, intervalMs);
        vscode.window.showInformationMessage('Chat monitoring started');
    }
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        vscode.window.showInformationMessage('Chat monitoring stopped');
    }
    async checkForNewChatContent() {
        // This method can be enhanced to automatically detect new chat content
        // For now, it's a placeholder for future automatic monitoring
        const clipboardContent = await vscode.env.clipboard.readText();
        if (clipboardContent && this.looksLikeChatContent(clipboardContent)) {
            // Could trigger automatic save or notification here
            console.log('New chat content detected in clipboard');
        }
    }
    getSupportedFormats() {
        return this.supportedFormats.map(format => format.name);
    }
    getFormatConfidence(content) {
        const detected = this.detectChatFormat(content);
        return {
            format: detected.name,
            confidence: detected.confidence
        };
    }
    // Future enhancement: Monitor specific VSCode panels/webviews
    // This would require more complex implementation to hook into
    // specific AI extension panels like Cursor, Augment, etc.
    async monitorAIExtensionPanels() {
        // This is a placeholder for future implementation
        // Would need to:
        // 1. Detect active AI extension panels
        // 2. Extract content from their webviews
        // 3. Parse the conversation structure
        // For now, we rely on user interaction (selection/clipboard)
        return null;
    }
}
exports.ChatMonitor = ChatMonitor;
//# sourceMappingURL=chatMonitor.js.map