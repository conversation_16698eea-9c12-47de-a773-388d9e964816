{"version": 3, "file": "chatMonitor.js", "sourceRoot": "", "sources": ["../src/chatMonitor.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AASjC,MAAa,WAAW;IAKpB;QAJQ,qBAAgB,GAAiB,EAAE,CAAC;QACpC,iBAAY,GAAY,KAAK,CAAC;QAC9B,uBAAkB,GAA0B,IAAI,CAAC;QAGrD,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEO,0BAA0B;QAC9B,IAAI,CAAC,gBAAgB,GAAG;YACpB,oBAAoB;YACpB;gBACI,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE;oBACN,YAAY;oBACZ,gBAAgB;oBAChB,+BAA+B;iBAClC;gBACD,MAAM,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC7D,UAAU,EAAE,GAAG;aAClB;YACD,gBAAgB;YAChB;gBACI,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE;oBACN,eAAe;oBACf,iBAAiB;oBACjB,oBAAoB;iBACvB;gBACD,MAAM,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC5D,UAAU,EAAE,GAAG;aAClB;YACD,gBAAgB;YAChB;gBACI,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE;oBACN,YAAY;oBACZ,aAAa;oBACb,gBAAgB;iBACnB;gBACD,MAAM,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBAC5D,UAAU,EAAE,GAAG;aAClB;YACD,iBAAiB;YACjB;gBACI,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE;oBACN,UAAU;oBACV,cAAc;oBACd,WAAW;oBACX,gBAAgB;iBACnB;gBACD,MAAM,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC7D,UAAU,EAAE,GAAG;aAClB;YACD,iBAAiB;YACjB;gBACI,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE;oBACN,WAAW;oBACX,gBAAgB;oBAChB,SAAS;oBACT,YAAY;iBACf;gBACD,MAAM,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC7D,UAAU,EAAE,GAAG;aAClB;SACJ,CAAC;IACN,CAAC;IAED,KAAK,CAAC,qBAAqB;QACvB,IAAI;YACA,2FAA2F;YAC3F,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,IAAI,eAAe,EAAE;gBACjB,OAAO,eAAe,CAAC;aAC1B;YAED,mEAAmE;YACnE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC1D,IAAI,gBAAgB,EAAE;gBAClB,OAAO,gBAAgB,CAAC;aAC3B;YAED,+CAA+C;YAC/C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzD,IAAI,eAAe,EAAE;gBACjB,OAAO,eAAe,CAAC;aAC1B;YAED,wDAAwD;YACxD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAClD,IAAI,aAAa,EAAE;gBACf,OAAO,aAAa,CAAC;aACxB;YAED,OAAO,IAAI,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,YAAY,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3E,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC3E,IAAI,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAE;gBACzC,OAAO,YAAY,CAAC;aACvB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC/D,IAAI,gBAAgB,IAAI,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;YACjE,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACvD,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CACxD,CAAC,KAAK,EAAE,IAAI,CAAC,EACb;gBACI,WAAW,EAAE,qDAAqD,MAAM,CAAC,IAAI,GAAG;gBAChF,cAAc,EAAE,IAAI;aACvB,CACJ,CAAC;YAEF,IAAI,kBAAkB,KAAK,KAAK,EAAE;gBAC9B,OAAO,gBAAgB,CAAC;aAC3B;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,iDAAiD;QACjD,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC;QAErD,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE;YAClC,IAAI,QAAQ,CAAC,UAAU,KAAK,WAAW,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;gBAC3E,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;oBACpC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAC/C,CAAC,KAAK,EAAE,IAAI,CAAC,EACb;wBACI,WAAW,EAAE,0BAA0B,QAAQ,CAAC,QAAQ,cAAc;wBACtE,cAAc,EAAE,IAAI;qBACvB,CACJ,CAAC;oBAEF,IAAI,SAAS,KAAK,KAAK,EAAE;wBACrB,OAAO,OAAO,CAAC;qBAClB;iBACJ;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACjD,MAAM,EAAE,+DAA+D;YACvE,WAAW,EAAE,wDAAwD;YACrE,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9C,OAAO,WAAW,CAAC;SACtB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,OAAe;QACxC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;IACnC,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACpC,IAAI,SAAS,GAAe;YACxB,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK;YACnB,UAAU,EAAE,CAAC;SAChB,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACxC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACnF,MAAM,UAAU,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7E,IAAI,UAAU,GAAG,SAAS,CAAC,UAAU,EAAE;gBACnC,SAAS,GAAG,EAAE,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC;aACzC;SACJ;QAED,yCAAyC;QACzC,MAAM,QAAQ,GAAG,+DAA+D,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/F,MAAM,aAAa,GAAG,iCAAiC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,mBAAmB,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;QAE3E,IAAI,QAAQ;YAAE,SAAS,CAAC,UAAU,IAAI,GAAG,CAAC;QAC1C,IAAI,aAAa;YAAE,SAAS,CAAC,UAAU,IAAI,GAAG,CAAC;QAC/C,IAAI,mBAAmB;YAAE,SAAS,CAAC,UAAU,IAAI,GAAG,CAAC;QAErD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,0BAA0B;IAClB,kBAAkB,CAAC,OAAe;QACtC,oEAAoE;QACpE,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrF,MAAM,YAAY,GAAG,+BAA+B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnE,OAAO,iBAAiB,IAAI,YAAY,CAAC;IAC7C,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACrC,uDAAuD;QACvD,MAAM,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;YAC9B,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAChG,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACrC,qDAAqD;QACrD,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;YAC3B,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACxF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACtC,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1F,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACtC,8CAA8C;QAC9C,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtF,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,iBAAiB,IAAI,iBAAiB,CAAC;IAClD,CAAC;IAED,mCAAmC;IAC5B,eAAe,CAAC,aAAqB,IAAI;QAC5C,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC7C,IAAI;gBACA,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;aACvC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;aACpD;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;QAEf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IACpE,CAAC;IAEM,cAAc;QACjB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QACD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,uEAAuE;QACvE,8DAA8D;QAC9D,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC/D,IAAI,gBAAgB,IAAI,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;YACjE,oDAAoD;YACpD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;SACzD;IACL,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,mBAAmB,CAAC,OAAe;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO;YACH,MAAM,EAAE,QAAQ,CAAC,IAAI;YACrB,UAAU,EAAE,QAAQ,CAAC,UAAU;SAClC,CAAC;IACN,CAAC;IAED,8DAA8D;IAC9D,8DAA8D;IAC9D,0DAA0D;IAClD,KAAK,CAAC,wBAAwB;QAClC,kDAAkD;QAClD,iBAAiB;QACjB,uCAAuC;QACvC,yCAAyC;QACzC,sCAAsC;QAEtC,6DAA6D;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAtTD,kCAsTC"}