"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const sessionManager_1 = require("./sessionManager");
const chatMonitor_1 = require("./chatMonitor");
const webviewProvider_1 = require("./webviewProvider");
function activate(context) {
    console.log('Chat Context Manager is now active!');
    const sessionManager = new sessionManager_1.SessionManager(context);
    const chatMonitor = new chatMonitor_1.ChatMonitor();
    const webviewProvider = new webviewProvider_1.WebviewProvider(context, sessionManager);
    // Register commands
    const saveSessionCommand = vscode.commands.registerCommand('chatContextManager.saveSession', async () => {
        try {
            const chatContent = await chatMonitor.getCurrentChatContent();
            if (chatContent) {
                // Parse messages before saving
                const messages = sessionManager.previewMessages(chatContent);
                const formatInfo = chatMonitor.getFormatConfidence(chatContent);
                if (messages.length === 0) {
                    vscode.window.showWarningMessage('No valid chat messages found in the content. Please check the format.');
                    return;
                }
                // Ask for session name
                const sessionName = await vscode.window.showInputBox({
                    prompt: `Enter a name for this chat session (${messages.length} messages, ${formatInfo.format} format detected)`,
                    placeHolder: 'My Chat Session'
                });
                if (!sessionName)
                    return;
                // Ask for tags (optional)
                const tagsInput = await vscode.window.showInputBox({
                    prompt: 'Enter tags for this session (comma-separated, optional)',
                    placeHolder: 'coding, javascript, debugging'
                });
                const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
                // Ask for category (optional)
                const existingCategories = await sessionManager.getAllCategories();
                const categoryOptions = ['General', 'Coding', 'Research', 'Learning', 'Debugging', 'Planning', ...existingCategories];
                const uniqueCategories = [...new Set(categoryOptions)];
                const category = await vscode.window.showQuickPick([...uniqueCategories, '$(add) Create New Category'], {
                    placeHolder: 'Select a category for this session (optional)',
                    ignoreFocusOut: true
                });
                let finalCategory = 'General';
                if (category && category !== '$(add) Create New Category') {
                    finalCategory = category;
                }
                else if (category === '$(add) Create New Category') {
                    const newCategory = await vscode.window.showInputBox({
                        prompt: 'Enter new category name',
                        placeHolder: 'My Category'
                    });
                    if (newCategory) {
                        finalCategory = newCategory;
                    }
                }
                const savedSession = await sessionManager.saveSession(sessionName, chatContent, tags, finalCategory);
                vscode.window.showInformationMessage(`✅ Session "${sessionName}" saved successfully! (${messages.length} messages, ${formatInfo.format} format, ${formatInfo.confidence.toFixed(2)} confidence)`);
            }
            else {
                const choice = await vscode.window.showWarningMessage('No chat content found. Please copy your chat conversation to clipboard or select text in editor first.', 'Show Instructions', 'Try Again');
                if (choice === 'Show Instructions') {
                    vscode.window.showInformationMessage('How to save chat: 1) Copy your chat conversation, 2) Run "Save Chat Session" command, OR select chat text in VSCode editor first.');
                }
                else if (choice === 'Try Again') {
                    vscode.commands.executeCommand('chatContextManager.saveSession');
                }
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to save session: ${error}`);
        }
    });
    const showSessionsCommand = vscode.commands.registerCommand('chatContextManager.showSessions', () => {
        webviewProvider.showSessions();
    });
    const generateMegaPromptCommand = vscode.commands.registerCommand('chatContextManager.generateMegaPrompt', async () => {
        const sessions = await sessionManager.getAllSessions();
        if (sessions.length === 0) {
            vscode.window.showInformationMessage('No saved sessions found.');
            return;
        }
        const sessionItems = sessions.map(session => ({
            label: session.name,
            description: new Date(session.timestamp).toLocaleString(),
            session: session
        }));
        const selectedItem = await vscode.window.showQuickPick(sessionItems, {
            placeHolder: 'Select a session to generate mega prompt'
        });
        if (selectedItem) {
            const megaPrompt = sessionManager.generateMegaPrompt(selectedItem.session);
            await vscode.env.clipboard.writeText(megaPrompt);
            vscode.window.showInformationMessage('Mega prompt copied to clipboard!');
        }
    });
    // Search sessions command
    const searchSessionsCommand = vscode.commands.registerCommand('chatContextManager.searchSessions', async () => {
        const query = await vscode.window.showInputBox({
            prompt: 'Enter search query (searches in names, content, and summaries)',
            placeHolder: 'javascript debugging'
        });
        if (query) {
            const results = await sessionManager.searchSessions(query);
            if (results.length === 0) {
                vscode.window.showInformationMessage(`No sessions found for "${query}"`);
                return;
            }
            const sessionItems = results.map(session => ({
                label: session.name,
                description: `${session.messages.length} messages • ${session.category} • ${new Date(session.timestamp).toLocaleDateString()}`,
                detail: session.tags?.join(', ') || 'No tags',
                session: session
            }));
            const selectedItem = await vscode.window.showQuickPick(sessionItems, {
                placeHolder: `Found ${results.length} sessions for "${query}"`
            });
            if (selectedItem) {
                webviewProvider.showSessionDetails(selectedItem.session);
            }
        }
    });
    // Show stats command
    const showStatsCommand = vscode.commands.registerCommand('chatContextManager.showStats', async () => {
        const stats = await sessionManager.getSessionStats();
        const message = `📊 Chat Context Manager Stats:

Total Sessions: ${stats.totalSessions}
Total Messages: ${stats.totalMessages}
Total Words: ${stats.totalWords.toLocaleString()}
Average Messages per Session: ${stats.averageMessagesPerSession.toFixed(1)}

Format Distribution:
${Object.entries(stats.formatDistribution).map(([format, count]) => `• ${format}: ${count}`).join('\n')}

Category Distribution:
${Object.entries(stats.categoryDistribution).map(([category, count]) => `• ${category}: ${count}`).join('\n')}`;
        vscode.window.showInformationMessage(message, { modal: true });
    });
    // Start monitoring command
    const startMonitoringCommand = vscode.commands.registerCommand('chatContextManager.startMonitoring', () => {
        chatMonitor.startMonitoring();
    });
    // Stop monitoring command
    const stopMonitoringCommand = vscode.commands.registerCommand('chatContextManager.stopMonitoring', () => {
        chatMonitor.stopMonitoring();
    });
    context.subscriptions.push(saveSessionCommand, showSessionsCommand, generateMegaPromptCommand, searchSessionsCommand, showStatsCommand, startMonitoringCommand, stopMonitoringCommand);
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map