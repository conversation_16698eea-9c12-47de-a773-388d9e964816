# تثبيت إضافة Chat Context Manager

## طريقة التثبيت

### الطريقة الأولى: من خلال VSCode مباشرة
1. افتح VSCode
2. اضغط `Ctrl+Shift+P` (أو `Cmd+Shift+P` على Mac) لفتح Command Palette
3. اكتب `Extensions: Install from VSIX...`
4. اختر ملف `chat-context-manager-0.0.1.vsix`
5. انتظر حتى يكتمل التثبيت
6. أعد تشغيل VSCode

### الطريقة الثانية: من خلال سطر الأوامر
```bash
code --install-extension chat-context-manager-0.0.1.vsix
```

## كيفية الاستخدام

### 1. حفظ محادثة
1. انسخ محادثتك مع الذكاء الاصطناعي إلى الحافظة
2. افتح Command Palette (`Ctrl+Shift+P`)
3. اك<PERSON><PERSON> `Chat Context: Save Chat Session`
4. أدخل اسماً للجلسة
5. ستحفظ الجلسة تلقائياً

### 2. عرض الجلسات المحفوظة
1. افتح Command Palette
2. اكتب `Chat Context: Show Saved Sessions`
3. ستفتح نافذة تعرض جميع الجلسات المحفوظة

### 3. إنشاء Mega Prompt
1. من نافذة الجلسات، اضغط على "Copy Mega Prompt" بجانب أي جلسة
2. أو استخدم Command Palette: `Chat Context: Generate Mega Prompt`
3. اختر الجلسة المطلوبة
4. سيتم نسخ الـ Mega Prompt إلى الحافظة

## الميزات
- ✅ حفظ المحادثات محلياً
- ✅ إنشاء Mega Prompts تلقائياً
- ✅ واجهة مستخدم سهلة
- ✅ دعم اللغة العربية
- ✅ يعمل مع جميع أدوات الذكاء الاصطناعي

## المتطلبات
- VSCode الإصدار 1.74.0 أو أحدث

## الدعم
إذا واجهت أي مشاكل، يرجى التواصل أو إنشاء issue في المشروع.

## ملاحظات مهمة
- جميع البيانات تحفظ محلياً على جهازك
- لا يتم إرسال أي بيانات للإنترنت
- الإضافة تدعم تنسيقات مختلفة من المحادثات
