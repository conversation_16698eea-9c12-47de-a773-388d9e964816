 SRS - Chat Context Manager Extension for VSCode
🧩 1. مقدمة
1.1 الغرض من المشروع
تهدف هذه الإضافة إلى مساعدة المستخدمين على حفظ واسترجاع المحادثات الطويلة التي تتم عبر أدوات الذكاء الاصطناعي داخل VSCode (مثل Cursor, Augment, WindSurf). توفر الأداة وسيلة ذكية لإعادة استخدام السياق من محادثات سابقة واستئنافها في جلسة جديدة دون فقدان التفاصيل.

1.2 نطاق النظام
الأداة تعمل بالكامل من داخل VSCode وتوفر واجهة رسومية GUI تسمح بـ:

مراقبة المحادثات الجارية.

حفظ المحادثات (بشكل كامل أو ملخص).

إنشاء Mega Prompt تلقائيًا.

استعراض المحادثات السابقة.

استئناف العمل بنفس السياق مع أي أداة ذكاء اصطناعي تدعم المحادثة النصية.

🎯 2. الوظائف الأساسية
2.1 مراقبة المحادثة
مراقبة محتوى النوافذ الجانبية (مثل شات Cursor أو Augment).

التقاط الرسائل المتبادلة بين المستخدم والـ Agent بشكل دوري (Polling أو Hook).

2.2 حفظ الجلسة
حفظ النص الكامل للمحادثة مع توقيتات ورسائل الطرفين.

حفظ نسخة "ملخصة" تحتوي على أهم النقاط فقط (يمكن توليدها تلقائيًا عبر نموذج تلخيص داخلي).

2.3 توليد Mega Prompt
عند الضغط على زر "Generate Mega Prompt"، يتم تحويل المحادثة السابقة إلى Prompt واحد ذكي جاهز لإعادة استخدامه في بداية محادثة جديدة.

2.4 واجهة الاستخدام (GUI)
قائمة بجميع المحادثات المحفوظة.

أزرار:

"Save Session"

"Show Sessions"

"Copy Mega Prompt"

"Delete Session"

🧱 3. المتطلبات غير الوظيفية
3.1 الأداء
لا يؤثر على سرعة تحرير الكود.

التخزين يتم محليًا ولا يتطلب إرسال بيانات لأي خادم.

3.2 الأمان
لا يتم إرسال أو مشاركة أي بيانات مع الإنترنت.

يتم تخزين الملفات في مجلد داخلي خاص بالإضافة.

3.3 التوافق
يجب أن تعمل مع أدوات الشات داخل VSCode مثل:

Cursor Chat

Augment AI

WindSurf

متوافقة مع Windows / macOS / Linux.

📁 4. هيكل الملفات
bash
Copy
Edit
ChatContextManager/
│
├── src/
│   ├── extension.ts           # منطق التفاعل الرئيسي مع VSCode
│   ├── commands.ts            # أوامر الحفظ والاسترجاع
│   └── utils.ts               # أدوات مثل توليد Mega Prompt
│
├── webview/
│   └── index.html             # واجهة المستخدم الرسومية
│
├── data/
│   └── sessions.json          # تخزين الجلسات المحفوظة
│
├── README.md
├── SRS.md
└── CHANGELOG.md
📌 5. حالات الاستخدام
✅ 5.1 حفظ محادثة جارية
المستخدم يضغط على زر “Save Session”.

يتم نسخ محتوى الشات من النافذة وحفظه.

✅ 5.2 استئناف محادثة قديمة
المستخدم يفتح نافذة “Sessions”.

يختار جلسة سابقة ويضغط “Generate Mega Prompt”.

يتم نسخ السياق تلقائيًا.

🔧 6. متطلبات التطوير
6.1 الأدوات والتقنيات
لغة: TypeScript

بيئة: VSCode Extension API

واجهة: HTML + JS داخل Webview

تخزين: JSON داخل مساحة الإضافة

🚀 7. مستقبل الإضافة (أفكار للتطوير لاحقًا)
دعم إنشاء ملخص تلقائي باستخدام OpenAI API (اختياري).

مزامنة الجلسات بين الأجهزة عبر GitHub أو Dropbox.

دعم التصدير إلى PDF أو Markdown.